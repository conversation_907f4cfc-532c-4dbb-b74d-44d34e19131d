import { Is<PERSON><PERSON>, <PERSON>NotEmpty, <PERSON><PERSON>ptional, IsString } from 'class-validator';

export class LegacyUserDto {
  @IsNotEmpty()
  @IsString()
  firstName: string;

  @IsNotEmpty()
  @IsString()
  lastName: string;

  @IsNotEmpty()
  @IsEmail()
  email: string;

  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @IsOptional()
  @IsString()
  country?: string;
}

export class RecaptchaVerificationDto {
  @IsNotEmpty()
  @IsString()
  token: string;

  @IsNotEmpty()
  @IsString()
  action: string;
}
