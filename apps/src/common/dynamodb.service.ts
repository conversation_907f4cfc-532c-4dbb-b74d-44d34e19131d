import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';

@Injectable()
export class DynamoDBService {
  async putObject(table, object): Promise<any> {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const params: AWS.DynamoDB.PutItemInput = {
      TableName: table,
      Item: object.Item,
    };
    await dynamoDB.put(params).promise();
  }
  async updateObject(params): Promise<any> {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    return await dynamoDB.update(params).promise();
  }
  async getObject(table, keys) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const params = {
      TableName: table,
      Key: keys,
    };
    return await dynamoDB.get(params).promise();
  }
  async queryObjects(params) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    return await dynamoDB.query(params).promise();
  }
  async deleteObject(params) {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    await dynamoDB.delete(params).promise();
  }
  async updateTableObject(table, key, object): Promise<any> {
    const dynamoDB = new AWS.DynamoDB.DocumentClient({
      region: process.env.REGION,
    });
    const { keys, update } = this.dynamodbUpdateRequest({
      keys: key,
      values: object,
    });
    const params = {
      TableName: table,
      Key: keys,
      ...update,
    };
    await dynamoDB.update(params).promise();
  }
  dynamodbUpdateRequest(params) {
    const { keys, values } = params;
    const sets = [];
    const removes = [];
    const expressionNames = {};
    const expValues = {};

    for (const [key, value] of Object.entries(values)) {
      expressionNames[`#${key}`] = key;
      if (value) {
        sets.push(`#${key} = :${key}`);
        expValues[`:${key}`] = value;
      } else {
        removes.push(`#${key}`);
      }
    }

    let expression = sets.length ? `SET ${sets.join(', ')}` : '';
    expression += removes.length ? ` REMOVE ${removes.join(', ')}` : '';
    return {
      keys,
      update: {
        UpdateExpression: expression,
        ExpressionAttributeNames: expressionNames,
        ExpressionAttributeValues: expValues,
      },
    };
  }
}
