import { Module } from '@nestjs/common';
import { DynamoDBService } from './dynamodb.service';
import { S3Service } from './s3.service';
import { SalesforceService } from './salesforce.service';
import { ExternalService } from './external.service';
import { RequestIdMiddleware } from './middleware';
import { LoggerModule } from '@gus-eip/loggers';
import { ConfigModule } from '@nestjs/config';
import { UtilityService } from './utility.service';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: ['.env.local', '.env'],
    }),
    LoggerModule.forRoot({
      region: process.env.REGION,
      logGroupName: process.env.LOGGER_LOG_GROUP_NAME,
      teamWebhookUrl: process.env.TEAMS_WEBHOOK_URL,
      isAlertNeeded: true,
      options: 'CloudWatchLogger',
    }),
  ],
  providers: [
    DynamoDBService,
    S3Service,
    SalesforceService,
    ExternalService,
    RequestIdMiddleware,
    UtilityService,
  ],
  exports: [
    DynamoDBService,
    S3Service,
    SalesforceService,
    ExternalService,
    RequestIdMiddleware,
    UtilityService,
  ],
})
export class CommonModule { }
